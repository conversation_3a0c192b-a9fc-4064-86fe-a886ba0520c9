name: 3 - Test Django Env Vars

on:
  workflow_dispatch:
  # push:
  #   branches:
  #     - main

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      DJANGO_DEBUG: 0
      # DJANGO_SECRET_KEY: not-good
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12' 
      - name: Setup Django Secret Key
        run: |
          MY_GEN_KEY=$(openssl rand -base64 32)
          echo "DJANGO_SECRET_KEY=$MY_GEN_KEY" >> $GITHUB_ENV
      - name: Install Requirements
        run: |
          python -m pip install pip --upgrade
          python -m pip install -r requirements.txt
      - name: Django Tests
        working-directory: ./src
        run: |
          python manage.py test