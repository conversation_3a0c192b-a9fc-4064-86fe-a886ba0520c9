# Generated by Django 5.0.6 on 2024-06-07 19:23

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("subscriptions", "0021_usersubscription_cancel_at_period_end"),
    ]

    operations = [
        migrations.AddField(
            model_name="usersubscription",
            name="timestamp",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="usersubscription",
            name="updated",
            field=models.DateTimeField(auto_now=True),
        ),
    ]
